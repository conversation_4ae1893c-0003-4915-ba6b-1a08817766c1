# Barcode Uniqueness Validation - Test Guide

## Overview
This document provides test cases to verify the barcode uniqueness validation functionality for products within the same store.

## Test Data Available
Based on the database, we have:

### Store 1 Products:
- Barcode: `4800361394161` (multiple products with this barcode exist in store 1)

### Store 2 Products:
- Barcode: `4800361394161` (exists in store 2)
- Barcode: `8675` (unique to store 2)
- Barcode: `443252` (unique to store 2)

## Test Cases

### Test Case 1: Search for Existing Barcode in Current Store
**Objective**: Verify that searching for a barcode that already exists in the current store shows validation warning and disables the "Use" button.

**Steps**:
1. Navigate to Products page for Store 1: `products/index.php?storeID=1`
2. Click "Add Item" to open the modal
3. Search for barcode: `4800361394161`
4. **Expected Result**:
   - Product should be found and displayed
   - Warning message should show: "This product is already added to your store"
   - "Use" button should be disabled and show "Already Added"
   - Status should show warning icon with validation message

### Test Case 2: Search for Barcode from Different Store
**Objective**: Verify that searching for a barcode that exists in a different store allows adding to current store.

**Steps**:
1. Navigate to Products page for Store 1: `products/index.php?storeID=1`
2. Click "Add Item" to open the modal
3. Search for barcode: `8675` (exists only in store 2)
4. **Expected Result**:
   - Product should be found and displayed
   - No warning message should appear
   - "Use" button should be enabled
   - Status should show success icon

### Test Case 3: Search for Non-existent Barcode
**Objective**: Verify that searching for a barcode that doesn't exist shows appropriate message.

**Steps**:
1. Navigate to Products page for any store
2. Click "Add Item" to open the modal
3. Search for barcode: `NONEXISTENT123`
4. **Expected Result**:
   - "Product not found" message should be displayed
   - No search results should be shown

### Test Case 4: Server-side Validation on Add
**Objective**: Verify that server-side validation prevents duplicate barcodes when adding products.

**Steps**:
1. Navigate to Products page for Store 1: `products/index.php?storeID=1`
2. Click "Add Item" to open the modal
3. Manually fill in the form with:
   - Product Name: "Test Product"
   - Product Code: `4800361394161` (existing barcode in store 1)
   - Category: Any valid category
   - Selling Price: 10.00
4. Click "Add record"
5. **Expected Result**:
   - Error message should appear: "This barcode already exists in your store. Each product must have a unique barcode within the store."
   - Product should NOT be added to the database

### Test Case 5: Successful Add with Unique Barcode
**Objective**: Verify that products with unique barcodes can be added successfully.

**Steps**:
1. Navigate to Products page for Store 1: `products/index.php?storeID=1`
2. Click "Add Item" to open the modal
3. Fill in the form with:
   - Product Name: "Test Product Unique"
   - Product Code: `UNIQUE123456` (new unique barcode)
   - Category: Any valid category
   - Selling Price: 15.00
4. Click "Add record"
5. **Expected Result**:
   - Success message should appear
   - Product should be added to the database
   - Product should appear in the products list

## UI/UX Validation Points

### Search Results Display:
- ✅ Product information is clearly displayed
- ✅ Validation messages are prominent and clear
- ✅ Button states (enabled/disabled) are visually distinct
- ✅ Warning icons and colors are used appropriately

### Button Behavior:
- ✅ Disabled buttons have appropriate styling
- ✅ Disabled buttons show helpful tooltips
- ✅ Enabled buttons function normally

### Status Messages:
- ✅ Success status shows green check icon
- ✅ Warning status shows yellow warning icon
- ✅ Error status shows red error icon
- ✅ Messages are clear and actionable

## Technical Implementation Verification

### Client-side (JavaScript):
- ✅ Search includes storeID parameter
- ✅ Response data includes validation fields
- ✅ UI updates based on validation status
- ✅ Click handlers respect validation state

### Server-side (PHP):
- ✅ fetch_product.php checks barcode uniqueness per store
- ✅ add_product.php validates before insertion
- ✅ Proper error messages are returned
- ✅ Database queries are scoped to store_id

### Database Queries:
- ✅ All queries include store_id filtering
- ✅ Barcode uniqueness is checked within store scope
- ✅ Cross-store barcode sharing is allowed

## Notes
- The validation allows the same barcode to exist in multiple different stores
- The validation only prevents duplicate barcodes within the same store
- Search functionality continues to work normally, showing products even if they can't be added
- All validation is performed on both client-side (for UX) and server-side (for security)
