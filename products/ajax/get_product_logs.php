<?php
require_once __DIR__ . '/../../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get DataTables parameters
    $draw = intval($_POST['draw'] ?? 1);
    $start = intval($_POST['start'] ?? 0);
    $length = intval($_POST['length'] ?? 10);
    $search_value = $_POST['search']['value'] ?? '';
    $order_column = intval($_POST['order'][0]['column'] ?? 0);
    $order_dir = $_POST['order'][0]['dir'] ?? 'desc'; // Default to newest first

    // Get and sanitize storeID parameter
    $storeID = isset($_POST['storeID']) ? intval(trim($_POST['storeID'])) : 0;

    // Validate storeID - must be a positive integer
    if ($storeID <= 0) {
        throw new Exception('Invalid or missing store ID');
    }

    // Column mapping for ordering - updated for new structure
    $columns = [
        0 => 'la.column_barcode',
        1 => 'c.category',
        2 => 'sp.product_image',
        3 => 'la.column_item',
        4 => 'sp.product_description',
        5 => 'la.value_audit',
        6 => 'la.log_user',
        7 => 'la.log_datetime'
    ];

    $order_column_name = $columns[$order_column] ?? 'la.log_datetime';

    // Base query with JOINs to get product and category information
    // Join with store_products using barcode and product name for matching
    $base_query = "FROM logs_audit la
                   LEFT JOIN store_products sp ON (la.column_barcode = sp.product_code OR la.column_item = sp.product_name)
                   AND sp.store_id = la.store_id
                   LEFT JOIN category c ON sp.category_id = c.id
                   WHERE la.store_id = ? AND la.log_type = 'product_management'";
    $params = [$storeID];
    $param_types = "i";

    // Add search filtering - updated for new columns
    if (!empty($search_value)) {
        $base_query .= " AND (la.log_user LIKE ? OR la.column_item LIKE ? OR la.column_barcode LIKE ? OR c.category LIKE ? OR sp.product_description LIKE ? OR la.log_datetime LIKE ? )";
        $search_param = "%{$search_value}%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param, $search_param]);
        $param_types .= "ssssss";
    }

    // Get total records count
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $stmt = $conn->prepare($total_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $total_result = $stmt->get_result();
    $total_records = $total_result->fetch_assoc()['total'];

    // Get filtered records count (same as total if no search)
    $filtered_records = $total_records;

    // Get actual data with ordering and pagination - updated for new columns
    $data_query = "SELECT la.id, la.log_datetime, la.log_user, la.column_item, la.column_barcode,
                          c.category, sp.product_description, sp.product_image, la.value_audit " .
                  $base_query .
                  " ORDER BY {$order_column_name} {$order_dir} LIMIT ?, ?";

    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";

    $stmt = $conn->prepare($data_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Format timestamp
        $timestamp = date('Y-m-d H:i:s', strtotime($row['log_datetime']));

        // Handle product image
        $image_html = '';
        if (!empty($row['product_image'])) {
            $image_html = '<img src="../img_products/' . htmlspecialchars($row['product_image']) . '" width="30" height="30" class="rounded" />';
        } else {
            $image_html = '<img src="../img_products/unknown-product.png" width="30" height="30" class="rounded" />';
        }

        $data[] = [
            htmlspecialchars($row['column_barcode'] ?? 'N/A'), // Barcode
            htmlspecialchars($row['category'] ?? 'Uncategorized'), // Category
            $image_html, // Image
            htmlspecialchars($row['column_item'] ?? 'N/A'), // Product name
            htmlspecialchars($row['product_description'] ?? 'N/A'), // Product description
            $row['value_audit'] ?? 'N/A',
            htmlspecialchars($row['log_user'] ?? 'System'), // Added By
            htmlspecialchars($timestamp) // Date Added
        ];
    }

    // Return DataTables format
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Error response
    $error_response = [
        'draw' => intval($_POST['draw'] ?? 1),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Server error occurred'
    ];
    
    echo json_encode($error_response);
}
