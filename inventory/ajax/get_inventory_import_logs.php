<?php
require_once __DIR__ . '/../../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get DataTables parameters
    $draw = intval($_POST['draw'] ?? 1);
    $start = intval($_POST['start'] ?? 0);
    $length = intval($_POST['length'] ?? 10);
    $search_value = $_POST['search']['value'] ?? '';
    $order_column = intval($_POST['order'][0]['column'] ?? 0);
    $order_dir = $_POST['order'][0]['dir'] ?? 'desc'; // Default to newest first

    // Get and sanitize storeID parameter
    $storeID = isset($_POST['storeID']) ? intval(trim($_POST['storeID'])) : 0;

    // Validate storeID - must be a positive integer
    if ($storeID <= 0) {
        throw new Exception('Invalid or missing store ID');
    }

    // Column mapping for ordering
    $columns = [
        0 => 'li.file_name',
        1 => 'li.log_status',
        2 => 'li.log_user',
        3 => 'li.log_datetime'
    ];

    $order_column_name = $columns[$order_column] ?? 'li.log_datetime';

    // Base query for logs_import table - filter for inventory-related import logs
    $base_query = "FROM logs_import li WHERE li.store_id = ? AND li.log_type = 'inventory_import'";
    $params = [$storeID];
    $param_types = "i";

    // Add search filtering
    if (!empty($search_value)) {
        $base_query .= " AND (li.file_name LIKE ? OR li.log_status LIKE ? OR li.log_user LIKE ? OR li.log_datetime LIKE ?)";
        $search_param = "%{$search_value}%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
        $param_types .= "ssss";
    }

    // Get total records count
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $stmt = $conn->prepare($total_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $total_result = $stmt->get_result();
    $total_records = $total_result->fetch_assoc()['total'];

    // Get filtered records count (same as total if no search)
    $filtered_records = $total_records;

    // Get actual data with ordering and pagination
    $data_query = "SELECT li.id, li.file_name, li.log_status, li.log_user, li.log_datetime " .
                  $base_query .
                  " ORDER BY {$order_column_name} {$order_dir} LIMIT ?, ?";

    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";

    $stmt = $conn->prepare($data_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Format timestamp
        $timestamp = date('Y-m-d H:i:s', strtotime($row['log_datetime']));

        // Format log status with color-coded badges
        $status_html = '';
        $log_status = $row['log_status'] ?? '';
        // if (strpos($log_status, 'success') !== false) {
        //     $status_html = '<span class="badge badge-success">SUCCESS</span>';
        // } elseif (strpos($log_status, 'error') !== false || strpos($log_status, 'failed') !== false) {
        //     $status_html = '<span class="badge badge-danger">ERROR</span>';
        // } elseif (strpos($log_status, 'warning') !== false) {
        //     $status_html = '<span class="badge badge-warning">WARNING</span>';
        // } else {
        //     $status_html = '<span class="badge badge-info">UNKNOWN</span>';
        // }

        $data[] = [
            htmlspecialchars($row['file_name'] ?? 'N/A'), // File
            $log_status, // Result (with badge styling)
            htmlspecialchars($row['log_user'] ?? 'System'), // User
            htmlspecialchars($timestamp) // Date
        ];
    }

    // Return DataTables format
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Error response
    $error_response = [
        'draw' => intval($_POST['draw'] ?? 1),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Server error occurred'
    ];
    
    echo json_encode($error_response);
}
