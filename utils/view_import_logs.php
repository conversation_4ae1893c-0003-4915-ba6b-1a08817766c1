<?php
/**
 * Import Logs Viewer
 * 
 * A utility script to view and analyze import logs from both logs_import and logs_audit tables.
 * This helps validate that the logging functionality is working correctly.
 */

require_once __DIR__ . '/../db_conn.php';

// Check if user is authenticated
if (!isset($_SESSION['auth']) || $_SESSION['auth'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Authentication required']);
    exit;
}

// Set content type
header('Content-Type: application/json');

try {
    // Get parameters
    $action = $_GET['action'] ?? 'list_imports';
    $store_id = isset($_GET['storeID']) ? intval($_GET['storeID']) : 0;
    $import_log_id = isset($_GET['import_log_id']) ? intval($_GET['import_log_id']) : 0;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
    
    switch ($action) {
        case 'list_imports':
            // List recent import operations
            $sql = "SELECT id, store_id, log_type, page_title, file_name, log_status, log_user, log_datetime 
                    FROM logs_import 
                    WHERE store_id = ? OR ? = 0
                    ORDER BY log_datetime DESC 
                    LIMIT ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('iii', $store_id, $store_id, $limit);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $imports = [];
            while ($row = $result->fetch_assoc()) {
                $imports[] = $row;
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'imports' => $imports,
                    'count' => count($imports)
                ]
            ]);
            break;
            
        case 'list_audit':
            // List audit trail for a specific import or all recent audits
            $sql = "SELECT id, store_id, log_type, page_title, column_barcode, column_item, value_audit, log_user, log_datetime 
                    FROM logs_audit 
                    WHERE store_id = ? OR ? = 0
                    ORDER BY log_datetime DESC 
                    LIMIT ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('iii', $store_id, $store_id, $limit);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $audits = [];
            while ($row = $result->fetch_assoc()) {
                // Parse the quantity field to extract status
                $qty_parts = explode('|', $row['value_audit']);
                $row['quantity'] = $qty_parts[0] ?? '';
                $row['processing_status'] = $qty_parts[1] ?? '';
                $audits[] = $row;
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'audits' => $audits,
                    'count' => count($audits)
                ]
            ]);
            break;
            
        case 'import_details':
            // Get detailed information about a specific import
            if ($import_log_id <= 0) {
                throw new Exception('Invalid import log ID');
            }
            
            // Get import summary
            $import_sql = "SELECT * FROM logs_import WHERE id = ?";
            $import_stmt = $conn->prepare($import_sql);
            $import_stmt->bind_param('i', $import_log_id);
            $import_stmt->execute();
            $import_result = $import_stmt->get_result();
            $import_data = $import_result->fetch_assoc();
            
            if (!$import_data) {
                throw new Exception('Import log not found');
            }
            
            // Get related audit entries (approximate by time range)
            $start_time = $import_data['log_datetime'];
            $end_time = date('Y-m-d H:i:s', strtotime($start_time) + 300); // 5 minutes after start
            
            $audit_sql = "SELECT * FROM logs_audit 
                         WHERE store_id = ? 
                         AND log_datetime BETWEEN ? AND ?
                         ORDER BY log_datetime ASC";
            
            $audit_stmt = $conn->prepare($audit_sql);
            $audit_stmt->bind_param('iss', $import_data['store_id'], $start_time, $end_time);
            $audit_stmt->execute();
            $audit_result = $audit_stmt->get_result();
            
            $audit_entries = [];
            while ($row = $audit_result->fetch_assoc()) {
                // Parse the quantity field to extract status
                $qty_parts = explode('|', $row['value_audit']);
                $row['quantity'] = $qty_parts[0] ?? '';
                $row['processing_status'] = $qty_parts[1] ?? '';
                $audit_entries[] = $row;
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'import' => $import_data,
                    'audit_entries' => $audit_entries,
                    'audit_count' => count($audit_entries)
                ]
            ]);
            break;
            
        case 'stats':
            // Get statistics about imports and processing
            $stats_sql = "SELECT 
                            COUNT(*) as total_imports,
                            SUM(CASE WHEN log_status LIKE 'success%' THEN 1 ELSE 0 END) as successful_imports,
                            SUM(CASE WHEN log_status LIKE 'error%' OR log_status LIKE 'fatal%' THEN 1 ELSE 0 END) as failed_imports,
                            SUM(CASE WHEN log_status LIKE 'partial%' THEN 1 ELSE 0 END) as partial_imports
                          FROM logs_import 
                          WHERE store_id = ? OR ? = 0";
            
            $stats_stmt = $conn->prepare($stats_sql);
            $stats_stmt->bind_param('ii', $store_id, $store_id);
            $stats_stmt->execute();
            $stats_result = $stats_stmt->get_result();
            $import_stats = $stats_result->fetch_assoc();
            
            $audit_stats_sql = "SELECT 
                                  COUNT(*) as total_rows_processed,
                                  SUM(CASE WHEN value_audit LIKE '%success%' THEN 1 ELSE 0 END) as successful_rows,
                                  SUM(CASE WHEN value_audit LIKE '%failed%' THEN 1 ELSE 0 END) as failed_rows,
                                  SUM(CASE WHEN value_audit LIKE '%skipped%' THEN 1 ELSE 0 END) as skipped_rows
                                FROM logs_audit 
                                WHERE store_id = ? OR ? = 0";
            
            $audit_stats_stmt = $conn->prepare($audit_stats_sql);
            $audit_stats_stmt->bind_param('ii', $store_id, $store_id);
            $audit_stats_stmt->execute();
            $audit_stats_result = $audit_stats_stmt->get_result();
            $audit_stats = $audit_stats_result->fetch_assoc();
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'import_stats' => $import_stats,
                    'audit_stats' => $audit_stats
                ]
            ]);
            break;
            
        default:
            throw new Exception('Invalid action specified');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
