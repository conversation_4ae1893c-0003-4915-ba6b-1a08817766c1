<?php
require_once __DIR__ . '/../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Only GET requests are allowed');
    }

    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];

    // Get cart items for the current cashier and store
    $stmt = $conn->prepare("SELECT * FROM orders 
                           WHERE store_id = ? AND cashier_id = ? AND status = 'Cart' 
                           ORDER BY order_date DESC");
    $stmt->bind_param('ii', $store_id, $cashier_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $cart_items = [];
    $subtotal = 0;
    
    while ($row = $result->fetch_assoc()) {
        $cart_items[] = [
            'id' => $row['id'],
            'product_code' => $row['product_code'],
            'category' => $row['category'],
            'product_name' => $row['product_name'],
            'quantity' => $row['quantity'],
            'price' => $row['price'],
            'discount' => $row['discount'],
            'payable' => $row['payable'],
            'order_date' => $row['order_date']
        ];
        
        $subtotal += $row['payable'];
    }

    // Calculate totals (you can modify these calculations as needed)
    $tax = 0; // No tax calculation in current system
    $total_discount = 0; // Sum of all discounts if needed
    $total = $subtotal - $total_discount + $tax;

    echo json_encode([
        'status' => 'success',
        'data' => [
            'cart_items' => $cart_items,
            'totals' => [
                'subtotal' => $subtotal,
                'tax' => $tax,
                'discount' => $total_discount,
                'total' => $total,
                'item_count' => count($cart_items)
            ]
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
