<?php include '../components/authentication.php';?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <?php include '../components/header.php';
        include '../db_conn.php';
        ?>
        
    </head>

    <body class="desktop chrome webkit nav-function-top mod-main-boxed mod-pace-custom mod-panel-icon nav-mobile-push nav-function-fixed header-function-fixed mod-clean-page-bg pace-done mod-bg-3 mod-fixed-bg " >
        <!-- DOC: script to save and load page settings -->
        <script src="../components/top-script.js"></script>
        <!-- BEGIN Page Wrapper -->
        <div class="page-wrapper">
            <div class="page-inner">
                <!-- BEGIN Left Aside -->
                <aside class="page-sidebar">
                    <div class="page-logo">
                        <a href="#" class="page-logo-link press-scale-down d-flex align-items-center position-relative" data-toggle="modal" data-target="#modal-shortcut">
                            <img src="img/logo.png" alt="SmartAdmin WebApp" aria-roledescription="logo">
                            <span class="page-logo-text mr-1">SmartAdmin WebApp</span>
                            <span class="position-absolute text-white opacity-50 small pos-top pos-right mr-2 mt-n2"></span>
                            <i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>
                        </a>
                    </div>
                    <!-- BEGIN PRIMARY NAVIGATION -->
                    <?php include '../components/primary-nav.php';?>
                    <!-- END PRIMARY NAVIGATION -->
                   
                </aside>
                <!-- END Left Aside -->
                <div class="page-content-wrapper">
                    <!-- BEGIN Page Header -->
                    <?php include '../components/page-header.php';?>
                    <!-- END Page Header -->
                    <!-- BEGIN Page Content -->
                    <!-- the #js-page-content id is needed for some plugins to initialize -->
                    <main id="js-page-content" role="main" class="page-content">
                       
                        <!-- Your main content goes below here: -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div id="panel-content" class="panel">
                                    <div class="panel-hdr">
                                        <h2>Order Management</h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div class="row">
                                                <!-- Left Column: Payment Section -->
                                                <div class="col-xl-6 col-lg-6 col-md-12">
                                                    <div class="card h-100">
                                                        <div class="card-header">
                                                            <h5 class="card-title mb-0">
                                                                <i class="fal fa-credit-card mr-2"></i>Payment Section
                                                            </h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <!-- Payment Methods -->
                                                            <div class="mb-4">
                                                                <h6 class="mb-3">Payment Methods</h6>
                                                                <div class="row">
                                                                    <div class="col-6 mb-2">
                                                                        <button class="btn btn-outline-primary btn-block">
                                                                            <i class="fal fa-money-bill mr-2"></i>Cash
                                                                        </button>
                                                                    </div>
                                                                    <div class="col-6 mb-2">
                                                                        <button class="btn btn-outline-warning btn-block">
                                                                            <i class="fal fa-handshake mr-2"></i>Credit
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Order Totals -->
                                                            <div class="mb-4">
                                                                <h6 class="mb-3">Order Summary</h6>
                                                                <div class="bg-light p-3 rounded">
                                                                    <div class="d-flex justify-content-between mb-2">
                                                                        <span>Subtotal:</span>
                                                                        <span id="subtotal">₱0.00</span>
                                                                    </div>
                                                                    <div class="d-flex justify-content-between mb-2">
                                                                        <span>Tax:</span>
                                                                        <span id="tax">₱0.00</span>
                                                                    </div>
                                                                    <div class="d-flex justify-content-between mb-2">
                                                                        <span>Discount:</span>
                                                                        <span id="discount">₱0.00</span>
                                                                    </div>
                                                                    <hr>
                                                                    <div class="d-flex justify-content-between font-weight-bold h5">
                                                                        <span>Total:</span>
                                                                        <span id="total">₱0.00</span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Transaction Controls -->
                                                            <div class="mb-3">
                                                                <h6 class="mb-3">Transaction Controls</h6>
                                                                <div class="row">
                                                                    <div class="col-12 mb-2">
                                                                        <button class="btn btn-success btn-block btn-lg">
                                                                            <i class="fal fa-check mr-2"></i>Complete Order
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

																								<?php 
																								if(isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned']) ){
																									if(empty($_GET['storeID']) || $_GET['storeID'] == 0){
																										$store_id = $_SESSION['auth_user']['sys_store_assigned'];
																									}else{
																										unset($_SESSION['auth_user']['sys_store_assigned']); // unset session auth_user
																										$store_id = $_GET['storeID'];
																									}
																								}
																								$sql_store = "SELECT * FROM store_info WHERE id = '$store_id' ";
																								$result_store = mysqli_query($conn, $sql_store);
																								$row_store = mysqli_fetch_assoc($result_store);

																								$sql_orders = "SELECT * FROM orders WHERE store_id = '$store_id' AND cashier_id = ".$_SESSION['auth_user']['uid']." AND status = 'Cart' ";
																								$result_orders = mysqli_query($conn,  $sql_orders);
																								?>

                                                <!-- Right Column: Product List Section -->
                                                <div class="col-xl-6 col-lg-6 col-md-12">
                                                    <div class="card h-100">
                                                        <div class="card-header">
                                                            <h5 class="card-title mb-0">
                                                                <i class="fal fa-shopping-cart mr-2"></i> <?= $row_store['client'] ?>
                                                            </h5>
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <!-- Search Bar -->
                                                            <div class="p-3 border-bottom">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" placeholder="Search product by code" id="productSearch">
                                                                    <div class="input-group-append">
                                                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                                                            <i class="fal fa-search"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div id="searchStatus" class="mt-2" style="display: none;">
                                                                    <small class="text-muted">
                                                                        <i class="fal fa-spinner fa-spin"></i> Searching...
                                                                    </small>
                                                                </div>
                                                            </div>

                                                            <!-- Product List -->
                                                            <div class="order-items-list" style="max-height: 500px; overflow-y: auto;">
                                                                <div id="orderItemsList">
                                                                    <!-- order items - these would be dynamically populated -->
																																		 <?php while($row_orders = mysqli_fetch_assoc($result_orders)): ?>
                                                                    <div class="order-item p-3 border-bottom">
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <div class="flex-grow-1 text-dark">
                                                                                <h6 class="mb-1"><?=$row_orders['product_name']; ?></h6>
																																								<small class="text-muted">Price : ₱<?=$row_orders['price']; ?></small>
                                                                                <small class="text-muted ml-4">SKU: <?=$row_orders['product_code']; ?></small>
																																								<small class="text-muted ml-4">Category: <?=$row_orders['category']; ?></small>
                                                                                <div class="mt-1">
                                                                                    <span class="badge badge-info" style="font-size: medium;">₱<?=$row_orders['payable']; ?></span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="input-group input-group-sm mr-2" style="width: 80px;">
                                                                                    <input type="number" class="form-control text-center quantity-input"
                                                                                           value="<?=$row_orders['quantity']; ?>"
                                                                                           min="1"
                                                                                           data-order-id="<?=$row_orders['id']; ?>"
                                                                                           data-original-value="<?=$row_orders['quantity']; ?>">
                                                                                </div>
                                                                                <button class="btn btn-sm btn-danger" onclick="delete_item(<?=$row_orders['id']; ?>)">
                                                                                    <i class="fal fa-trash"></i>
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
																																		 <?php endwhile; ?>

                                                                    <!-- Empty state when no items -->
																																		 <?php if(mysqli_num_rows($result_orders) <= 0): ?>
                                                                    <div class="empty-state p-4 text-center">
                                                                        <i class="fal fa-shopping-cart fa-3x text-muted mb-3"></i>
                                                                        <h6 class="text-muted">No items in order</h6>
                                                                        <p class="text-muted small">Add products to get started</p>
                                                                    </div>
																																		 <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </main>
                    <!-- this overlay is activated only when mobile menu is triggered -->
                    <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div> <!-- END Page Content -->
                    <!-- BEGIN Page Footer -->
                    <?php include '../components/footer.php';?>
                    
                </div>
            </div>
        </div>

       <?php include '../components/bottom.php';?>

       <script>
       $(document).ready(function() {
           let searchTimeout;
           const $productSearch = $('#productSearch');
           const $searchStatus = $('#searchStatus');
           const $orderItemsList = $('#orderItemsList');

           // Get store ID from URL or session
           const urlParams = new URLSearchParams(window.location.search);
           const storeID = urlParams.get('storeID') || '';

           // Auto-search functionality with debouncing
           $productSearch.on('input', function() {
               const productCode = $(this).val().trim();

               // Clear previous timeout
               clearTimeout(searchTimeout);

               // Hide status if input is empty
               if (productCode === '') {
                   $searchStatus.hide();
                   return;
               }

               // Set timeout for debouncing (wait 500ms after user stops typing)
               searchTimeout = setTimeout(function() {
                   searchAndAddProduct(productCode);
               }, 500);
           });

           // Manual search button click
           $('#searchBtn').on('click', function() {
               const productCode = $productSearch.val().trim();
               if (productCode !== '') {
                   searchAndAddProduct(productCode);
               }
           });

           // Enter key press
           $productSearch.on('keypress', function(e) {
               if (e.which === 13) { // Enter key
                   e.preventDefault();
                   const productCode = $(this).val().trim();
                   if (productCode !== '') {
                       clearTimeout(searchTimeout);
                       searchAndAddProduct(productCode);
                   }
               }
           });

           // Quantity input change handler
           $(document).on('change blur', '.quantity-input', function() {
               const $input = $(this);
               const orderId = $input.data('order-id');
               const newQuantity = parseInt($input.val());
               const originalQuantity = parseInt($input.data('original-value'));

               // Validate quantity
               if (isNaN(newQuantity) || newQuantity < 1) {
                   $input.val(originalQuantity);
                   alert('Quantity must be at least 1');
                   return;
               }

               // Only update if quantity changed
               if (newQuantity !== originalQuantity) {
                   updateCartQuantity(orderId, newQuantity, $input);
               }
           });

           function searchAndAddProduct(productCode) {
               console.log('Searching for product:', productCode);

               // Show loading status
               $searchStatus.html('<small class="text-muted"><i class="fal fa-spinner fa-spin"></i> Searching...</small>').show();

               // AJAX request to add product to cart
               $.ajax({
                   url: 'add_to_cart.php' + (storeID ? '?storeID=' + storeID : ''),
                   type: 'POST',
                   data: {
                       product_code: productCode
                   },
                   success: function(response, textStatus, xhr) {
                       // Handle both JSON and text responses
                       let parsedResponse;
                       try {
                           if (typeof response === 'string') {
                               parsedResponse = JSON.parse(response);
                           } else {
                               parsedResponse = response;
                           }
                       } catch (e) {
                           console.error('JSON Parse Error:', e);
                           console.error('Raw Response:', response);
                           showError('Invalid server response format');
                           return;
                       }

                       if (parsedResponse.status === 'success') {
                           // Show success message
                           $searchStatus.html('<small class="text-success"><i class="fal fa-check"></i> ' + parsedResponse.message + '</small>').show();

                           // Clear search input
                           $productSearch.val('');

                           // Refresh cart display instead of reloading page
                           refreshCartDisplay();

                           // Hide status after 3 seconds
                           setTimeout(function() {
                               $searchStatus.fadeOut();
                           }, 3000);
                       } else {
                           showError(parsedResponse.message || 'Unknown error occurred');
                       }
                   },
                   error: function(xhr, status, error) {
                       console.error('AJAX Error:', {xhr, status, error});
                       console.error('Response Text:', xhr.responseText);

                       let errorMessage = 'Network error occurred';

                       // Try to parse error response
                       if (xhr.responseText) {
                           try {
                               const response = JSON.parse(xhr.responseText);
                               errorMessage = response.message || errorMessage;
                           } catch (e) {
                               // If JSON parsing fails, check for common error patterns
                               if (xhr.responseText.includes('Fatal error')) {
                                   errorMessage = 'Server configuration error';
                               } else if (xhr.responseText.includes('Warning')) {
                                   errorMessage = 'Server warning - check logs';
                               } else {
                                   errorMessage = 'Server error: ' + xhr.status + ' - ' + xhr.statusText;
                               }
                           }
                       }

                       showError(errorMessage);
                   }
               });
           }

           function showError(message) {
               $searchStatus.html('<small class="text-danger"><i class="fal fa-exclamation-triangle"></i> ' + message + '</small>').show();

               // Hide error after 5 seconds
               setTimeout(function() {
                   $searchStatus.fadeOut();
               }, 5000);
           }

           function refreshCartDisplay() {
               // Get store ID from URL or session
               const urlParams = new URLSearchParams(window.location.search);
               const storeID = urlParams.get('storeID') || '';

               console.log('Refreshing cart display...');

               $.ajax({
                   url: 'get_cart_items.php' + (storeID ? '?storeID=' + storeID : ''),
                   type: 'GET',
                   success: function(response, textStatus, xhr) {
                       console.log('Cart refresh response:', {
                           status: xhr.status,
                           statusText: xhr.statusText,
                           responseText: xhr.responseText,
                           response: response
                       });

                       let parsedResponse;
                       try {
                           if (typeof response === 'string') {
                               parsedResponse = JSON.parse(response);
                           } else {
                               parsedResponse = response;
                           }
                       } catch (e) {
                           console.error('JSON Parse Error in refreshCartDisplay:', e);
                           console.error('Raw Response:', response);
                           console.error('Response Type:', typeof response);
                           return;
                       }

                       if (parsedResponse.status === 'success') {
                           console.log('Cart data received:', parsedResponse.data);
                           updateCartHTML(parsedResponse.data.cart_items);
                           updateTotals(parsedResponse.data.totals);
                       } else {
                           console.error('Error refreshing cart:', parsedResponse.message);
                       }
                   },
                   error: function(xhr, status, error) {
                       console.error('Error refreshing cart display:', {
                           xhr: xhr,
                           status: status,
                           error: error,
                           responseText: xhr.responseText,
                           statusCode: xhr.status
                       });
                   }
               });
           }

           function updateCartHTML(cartItems) {
               console.log('Updating cart HTML with items:', cartItems);
               const $orderItemsList = $('#orderItemsList');

               if (!$orderItemsList.length) {
                   console.error('orderItemsList element not found!');
                   return;
               }

               if (cartItems.length === 0) {
                   console.log('Showing empty cart state');
                   // Show empty state
                   $orderItemsList.html(`
                       <div class="empty-state p-4 text-center">
                           <i class="fal fa-shopping-cart fa-3x text-muted mb-3"></i>
                           <h6 class="text-muted">No items in order</h6>
                           <p class="text-muted small">Add products to get started</p>
                       </div>
                   `);
               } else {
                   console.log('Building HTML for', cartItems.length, 'cart items');
                   // Build cart items HTML
                   let html = '';
                   cartItems.forEach(function(item) {
                       html += `
                           <div class="order-item p-3 border-bottom">
                               <div class="d-flex justify-content-between align-items-center">
                                   <div class="flex-grow-1 text-dark">
                                       <h6 class="mb-1">${escapeHtml(item.product_name)}</h6>
                                       <small class="text-muted">Price : ₱${item.price}</small>
                                       <small class="text-muted ml-4">SKU: ${escapeHtml(item.product_code)}</small>
                                       <small class="text-muted ml-4">Category: ${escapeHtml(item.category || '')}</small>
                                       <div class="mt-1">
                                           <span class="badge badge-info" style="font-size: medium;">₱${item.payable}</span>
                                       </div>
                                   </div>
                                   <div class="d-flex align-items-center">
                                       <div class="input-group input-group-sm mr-2" style="width: 80px;">
                                           <input type="number" class="form-control text-center quantity-input"
                                                  value="${item.quantity}"
                                                  min="1"
                                                  data-order-id="${item.id}"
                                                  data-original-value="${item.quantity}">
                                       </div>
                                       <button class="btn btn-sm btn-danger" onclick="delete_item(${item.id})">
                                           <i class="fal fa-trash"></i>
                                       </button>
                                   </div>
                               </div>
                           </div>
                       `;
                   });
                   $orderItemsList.html(html);
                   console.log('Cart HTML updated successfully');
               }
           }

           function updateTotals(totals) {
               console.log('Updating totals:', totals);

               const $subtotal = $('#subtotal');
               const $tax = $('#tax');
               const $discount = $('#discount');
               const $total = $('#total');

               if (!$subtotal.length) console.error('subtotal element not found!');
               if (!$tax.length) console.error('tax element not found!');
               if (!$discount.length) console.error('discount element not found!');
               if (!$total.length) console.error('total element not found!');

               $subtotal.text('₱' + totals.subtotal.toFixed(2));
               $tax.text('₱' + totals.tax.toFixed(2));
               $discount.text('₱' + totals.discount.toFixed(2));
               $total.text('₱' + totals.total.toFixed(2));

               console.log('Totals updated successfully');
           }

           function escapeHtml(text) {
               const div = document.createElement('div');
               div.textContent = text;
               return div.innerHTML;
           }

           function updateCartQuantity(orderId, quantity, $input) {
               // Get store ID from URL or session
               const urlParams = new URLSearchParams(window.location.search);
               const storeID = urlParams.get('storeID') || '';

               // Disable input while updating
               $input.prop('disabled', true);

               $.ajax({
                   url: 'update_cart_quantity.php' + (storeID ? '?storeID=' + storeID : ''),
                   type: 'POST',
                   data: {
                       order_id: orderId,
                       quantity: quantity
                   },
                   dataType: 'json',
                   success: function(response, textStatus, xhr) {
                       let parsedResponse;
                       try {
                           if (typeof response === 'string') {
                               parsedResponse = JSON.parse(response);
                           } else {
                               parsedResponse = response;
                           }
                       } catch (e) {
                           console.error('JSON Parse Error in updateCartQuantity:', e);
                           $input.val($input.data('original-value'));
                           alert('Invalid server response format');
                           return;
                       }

                       if (parsedResponse.status === 'success') {
                           // Update the original value
                           $input.data('original-value', quantity);

                           // Refresh cart display instead of reloading page
                           refreshCartDisplay();
                       } else {
                           // Revert to original value
                           $input.val($input.data('original-value'));
                           alert('Error: ' + (parsedResponse.message || 'Unknown error occurred'));
                       }
                   },
                   error: function(xhr, status, error) {
                       // Revert to original value
                       $input.val($input.data('original-value'));

                       console.error('AJAX Error in updateCartQuantity:', {xhr, status, error});
                       console.error('Response Text:', xhr.responseText);

                       let errorMessage = 'Network error occurred';

                       if (xhr.responseText) {
                           try {
                               const response = JSON.parse(xhr.responseText);
                               errorMessage = response.message || errorMessage;
                           } catch (e) {
                               if (xhr.responseText.includes('Fatal error')) {
                                   errorMessage = 'Server configuration error';
                               } else if (xhr.responseText.includes('Warning')) {
                                   errorMessage = 'Server warning - check logs';
                               } else {
                                   errorMessage = 'Server error: ' + xhr.status + ' - ' + xhr.statusText;
                               }
                           }
                       }

                       alert('Error: ' + errorMessage);
                   },
                   complete: function() {
                       // Re-enable input
                       $input.prop('disabled', false);
                   }
               });
           }
       });

       // Global function for deleting cart items
       function delete_item(order_id) {
           if (!confirm('Are you sure you want to remove this item from the cart?')) {
               return;
           }

           // Get store ID from URL or session
           const urlParams = new URLSearchParams(window.location.search);
           const storeID = urlParams.get('storeID') || '';

           $.ajax({
               url: 'delete_cart_item.php' + (storeID ? '?storeID=' + storeID : ''),
               type: 'POST',
               data: {
                   order_id: order_id
               },
               dataType: 'json',
               success: function(response, textStatus, xhr) {
                   let parsedResponse;
                   try {
                       if (typeof response === 'string') {
                           parsedResponse = JSON.parse(response);
                       } else {
                           parsedResponse = response;
                       }
                   } catch (e) {
                       console.error('JSON Parse Error in delete_item:', e);
                       alert('Invalid server response format');
                       return;
                   }

                   if (parsedResponse.status === 'success') {
                       // Refresh cart display instead of reloading page
                       refreshCartDisplay();

                       // Show brief success message (optional)
                       console.log(parsedResponse.message);
                   } else {
                       alert('Error: ' + (parsedResponse.message || 'Unknown error occurred'));
                   }
               },
               error: function(xhr, status, error) {
                   console.error('AJAX Error in delete_item:', {xhr, status, error});
                   console.error('Response Text:', xhr.responseText);

                   let errorMessage = 'Network error occurred';

                   if (xhr.responseText) {
                       try {
                           const response = JSON.parse(xhr.responseText);
                           errorMessage = response.message || errorMessage;
                       } catch (e) {
                           if (xhr.responseText.includes('Fatal error')) {
                               errorMessage = 'Server configuration error';
                           } else if (xhr.responseText.includes('Warning')) {
                               errorMessage = 'Server warning - check logs';
                           } else {
                               errorMessage = 'Server error: ' + xhr.status + ' - ' + xhr.statusText;
                           }
                       }
                   }

                   alert('Error: ' + errorMessage);
               }
           });
       }
       </script>
    </body>
    <!-- END Body -->
</html>
