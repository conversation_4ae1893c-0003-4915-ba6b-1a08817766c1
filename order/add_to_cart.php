<?php
require_once __DIR__ . '/../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get and validate input
    $product_code = trim($_POST['product_code'] ?? '');
    if (empty($product_code)) {
        throw new Exception('Product code is required');
    }

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];
    $cashier_name = $_SESSION['auth_user']['fname'] . ' ' . $_SESSION['auth_user']['lname'];

    // Search for product in store_products table
    $stmt = $conn->prepare("SELECT sp.*, c.category 
                           FROM store_products sp 
                           LEFT JOIN category c ON sp.category_id = c.id 
                           WHERE sp.product_code = ? AND sp.store_id = ? 
                           LIMIT 1");
    $stmt->bind_param('si', $product_code, $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Product not found in store inventory');
    }

    $product = $result->fetch_assoc();

    // Check if product is already in cart for this cashier
    $stmt = $conn->prepare("SELECT id, quantity FROM orders 
                           WHERE product_code = ? AND store_id = ? AND cashier_id = ? AND status = 'Cart'");
    $stmt->bind_param('sii', $product_code, $store_id, $cashier_id);
    $stmt->execute();
    $existing_order = $stmt->get_result()->fetch_assoc();

    if ($existing_order) {
        // Update quantity if product already exists in cart
        $new_quantity = $existing_order['quantity'] + 1;
        $stmt = $conn->prepare("UPDATE orders SET quantity = ?, order_date = NOW() WHERE id = ?");
        $stmt->bind_param('ii', $new_quantity, $existing_order['id']);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to update cart item: ' . $stmt->error);
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'Product quantity updated in cart',
            'action' => 'updated',
            'data' => [
                'order_id' => $existing_order['id'],
                'product_name' => $product['product_name'],
                'product_code' => $product_code,
                'new_quantity' => $new_quantity,
                'price' => $product['selling_price']
            ]
        ]);
    } else {
        // Insert new product into cart (orders table)
        $stmt = $conn->prepare("INSERT INTO orders 
                               (product_code, category, product_name, quantity, price, discount, 
                                payment_option, amount_received, changed, cashier_name, cashier_id, 
                                store_id, status, order_date) 
                               VALUES (?, ?, ?, 1, ?, 0, 'Cash', 0, 0, ?, ?, ?, 'Cart', NOW())");
        
        $stmt->bind_param('sssdsis', 
            $product['product_code'],
            $product['category'],
            $product['product_name'],
            $product['selling_price'],
            $cashier_name,
            $cashier_id,
            $store_id
        );

        if (!$stmt->execute()) {
            throw new Exception('Failed to add product to cart: ' . $stmt->error);
        }

        $order_id = $stmt->insert_id;

        echo json_encode([
            'status' => 'success',
            'message' => 'Product added to cart successfully',
            'action' => 'added',
            'data' => [
                'order_id' => $order_id,
                'product_name' => $product['product_name'],
                'product_code' => $product_code,
                'quantity' => 1,
                'price' => $product['selling_price'],
                'category' => $product['category']
            ]
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
