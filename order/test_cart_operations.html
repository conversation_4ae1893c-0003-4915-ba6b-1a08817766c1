<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Operations Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 15px; }
        input { margin: 5px; padding: 5px; }
    </style>
</head>
<body>
    <h1>Cart Operations Test Suite</h1>
    
    <div class="test-section">
        <h2>1. Test Get Cart Items</h2>
        <button id="testGetCart">Get Current Cart</button>
        <div id="getCartResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test Add Product to Cart</h2>
        <input type="text" id="productCode" placeholder="Product Code" value="4800361394161">
        <button id="testAddProduct">Add Product</button>
        <div id="addProductResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Update Quantity</h2>
        <input type="number" id="orderId" placeholder="Order ID" min="1">
        <input type="number" id="newQuantity" placeholder="New Quantity" min="1" value="2">
        <button id="testUpdateQuantity">Update Quantity</button>
        <div id="updateQuantityResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Delete Item</h2>
        <input type="number" id="deleteOrderId" placeholder="Order ID" min="1">
        <button id="testDeleteItem">Delete Item</button>
        <div id="deleteItemResult"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Test Complete Flow</h2>
        <button id="testCompleteFlow">Run Complete Test Flow</button>
        <div id="completeFlowResult"></div>
    </div>
    
    <script>
        function logResult(elementId, title, success, data, error = null) {
            const $element = $('#' + elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            let html = `<h4>${title} - ${timestamp}</h4>`;
            
            if (success) {
                html += `<div class="success">✅ Success</div>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                html += `<div class="error">❌ Error</div>`;
                html += `<pre>${error}</pre>`;
                if (data) {
                    html += `<div class="info">Response Data:</div>`;
                    html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            }
            
            $element.html(html);
        }
        
        function makeAjaxCall(url, method, data, successCallback, errorCallback) {
            console.log(`Making ${method} request to ${url}`, data);
            
            $.ajax({
                url: url,
                type: method,
                data: data,
                success: function(response, textStatus, xhr) {
                    console.log(`${method} ${url} - Success:`, response);
                    successCallback(response, xhr);
                },
                error: function(xhr, status, error) {
                    console.error(`${method} ${url} - Error:`, {xhr, status, error});
                    errorCallback(xhr, status, error);
                }
            });
        }
        
        $('#testGetCart').click(function() {
            makeAjaxCall(
                'get_cart_items.php',
                'GET',
                {},
                function(response, xhr) {
                    logResult('getCartResult', 'Get Cart Items', true, response);
                },
                function(xhr, status, error) {
                    logResult('getCartResult', 'Get Cart Items', false, null, 
                        `Status: ${xhr.status}\nError: ${error}\nResponse: ${xhr.responseText}`);
                }
            );
        });
        
        $('#testAddProduct').click(function() {
            const productCode = $('#productCode').val();
            if (!productCode) {
                alert('Please enter a product code');
                return;
            }
            
            makeAjaxCall(
                'add_to_cart.php',
                'POST',
                { product_code: productCode },
                function(response, xhr) {
                    logResult('addProductResult', 'Add Product', true, response);
                },
                function(xhr, status, error) {
                    logResult('addProductResult', 'Add Product', false, null,
                        `Status: ${xhr.status}\nError: ${error}\nResponse: ${xhr.responseText}`);
                }
            );
        });
        
        $('#testUpdateQuantity').click(function() {
            const orderId = $('#orderId').val();
            const quantity = $('#newQuantity').val();
            
            if (!orderId || !quantity) {
                alert('Please enter both Order ID and New Quantity');
                return;
            }
            
            makeAjaxCall(
                'update_cart_quantity.php',
                'POST',
                { order_id: orderId, quantity: quantity },
                function(response, xhr) {
                    logResult('updateQuantityResult', 'Update Quantity', true, response);
                },
                function(xhr, status, error) {
                    logResult('updateQuantityResult', 'Update Quantity', false, null,
                        `Status: ${xhr.status}\nError: ${error}\nResponse: ${xhr.responseText}`);
                }
            );
        });
        
        $('#testDeleteItem').click(function() {
            const orderId = $('#deleteOrderId').val();
            
            if (!orderId) {
                alert('Please enter an Order ID');
                return;
            }
            
            makeAjaxCall(
                'delete_cart_item.php',
                'POST',
                { order_id: orderId },
                function(response, xhr) {
                    logResult('deleteItemResult', 'Delete Item', true, response);
                },
                function(xhr, status, error) {
                    logResult('deleteItemResult', 'Delete Item', false, null,
                        `Status: ${xhr.status}\nError: ${error}\nResponse: ${xhr.responseText}`);
                }
            );
        });
        
        $('#testCompleteFlow').click(function() {
            const $result = $('#completeFlowResult');
            $result.html('<div class="info">Running complete test flow...</div>');
            
            let flowResults = [];
            
            // Step 1: Get initial cart state
            makeAjaxCall('get_cart_items.php', 'GET', {}, 
                function(response) {
                    flowResults.push('✅ Step 1: Get initial cart - Success');
                    
                    // Step 2: Add a product
                    makeAjaxCall('add_to_cart.php', 'POST', { product_code: '4800361394161' },
                        function(response) {
                            flowResults.push('✅ Step 2: Add product - Success');
                            
                            // Step 3: Get cart again to verify addition
                            makeAjaxCall('get_cart_items.php', 'GET', {},
                                function(response) {
                                    flowResults.push('✅ Step 3: Verify product added - Success');
                                    
                                    if (response.data && response.data.cart_items && response.data.cart_items.length > 0) {
                                        const firstItem = response.data.cart_items[0];
                                        
                                        // Step 4: Update quantity
                                        makeAjaxCall('update_cart_quantity.php', 'POST', 
                                            { order_id: firstItem.id, quantity: 3 },
                                            function(response) {
                                                flowResults.push('✅ Step 4: Update quantity - Success');
                                                
                                                // Step 5: Verify quantity update
                                                makeAjaxCall('get_cart_items.php', 'GET', {},
                                                    function(response) {
                                                        flowResults.push('✅ Step 5: Verify quantity update - Success');
                                                        
                                                        // Display final results
                                                        $result.html(`
                                                            <h4>Complete Flow Test Results</h4>
                                                            <div class="success">${flowResults.join('<br>')}</div>
                                                            <div class="info">Final Cart State:</div>
                                                            <pre>${JSON.stringify(response, null, 2)}</pre>
                                                        `);
                                                    },
                                                    function(xhr, status, error) {
                                                        flowResults.push('❌ Step 5: Verify quantity update - Failed');
                                                        $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                                                    }
                                                );
                                            },
                                            function(xhr, status, error) {
                                                flowResults.push('❌ Step 4: Update quantity - Failed');
                                                $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                                            }
                                        );
                                    } else {
                                        flowResults.push('❌ Step 3: No items found in cart after addition');
                                        $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                                    }
                                },
                                function(xhr, status, error) {
                                    flowResults.push('❌ Step 3: Verify product added - Failed');
                                    $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                                }
                            );
                        },
                        function(xhr, status, error) {
                            flowResults.push('❌ Step 2: Add product - Failed');
                            $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                        }
                    );
                },
                function(xhr, status, error) {
                    flowResults.push('❌ Step 1: Get initial cart - Failed');
                    $result.html(`<div class="error">${flowResults.join('<br>')}</div>`);
                }
            );
        });
    </script>
</body>
</html>
