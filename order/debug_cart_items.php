<?php
// Debug endpoint to show current cart items and their details
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../db_conn.php';

header('Content-Type: application/json');

try {
    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];

    // Get all cart items with detailed information
    $stmt = $conn->prepare("SELECT id, product_code, product_name, quantity, price, payable, 
                                   cashier_id, store_id, status, order_date 
                           FROM orders 
                           WHERE store_id = ? AND cashier_id = ? AND status = 'Cart' 
                           ORDER BY order_date DESC");
    $stmt->bind_param('ii', $store_id, $cashier_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $cart_items = [];
    while ($row = $result->fetch_assoc()) {
        $cart_items[] = $row;
    }

    echo json_encode([
        'status' => 'success',
        'debug_info' => [
            'current_cashier_id' => $cashier_id,
            'current_store_id' => $store_id,
            'session_auth_user' => $_SESSION['auth_user'] ?? 'not set',
            'get_storeID' => $_GET['storeID'] ?? 'not set'
        ],
        'cart_items' => $cart_items,
        'total_items' => count($cart_items)
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug_info' => [
            'session_exists' => isset($_SESSION),
            'auth_user_exists' => isset($_SESSION['auth_user']),
            'get_params' => $_GET
        ]
    ]);
}
