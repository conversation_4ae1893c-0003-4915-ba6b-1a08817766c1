<?php
// Suppress PHP warnings/notices for clean JSON output
error_reporting(0);
ini_set('display_errors', 0);

require_once __DIR__ . '/../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

// Clear any output buffer to prevent corruption
if (ob_get_level()) {
    ob_clean();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get and validate input
    $order_id = intval($_POST['order_id'] ?? 0);
    if ($order_id <= 0) {
        throw new Exception('Valid order ID is required');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Verify the order belongs to the current cashier and store, and is in Cart status
    $stmt = $conn->prepare("SELECT id, product_name FROM orders 
                           WHERE id = ? AND cashier_id = ? AND store_id = ? AND status = 'Cart'");
    $stmt->bind_param('iii', $order_id, $cashier_id, $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Order item not found or access denied');
    }

    $order = $result->fetch_assoc();

    // Delete the order item
    $stmt = $conn->prepare("DELETE FROM orders WHERE id = ? AND cashier_id = ? AND store_id = ? AND status = 'Cart'");
    $stmt->bind_param('iii', $order_id, $cashier_id, $store_id);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to delete cart item: ' . $stmt->error);
    }

    if ($stmt->affected_rows === 0) {
        throw new Exception('No item was deleted. Item may not exist or access denied.');
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Item removed from cart successfully',
        'data' => [
            'order_id' => $order_id,
            'product_name' => $order['product_name']
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
