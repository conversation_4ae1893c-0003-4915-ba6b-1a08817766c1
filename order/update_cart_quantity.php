<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once __DIR__ . '/../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

// Clear any output buffer to prevent corruption
if (ob_get_level()) {
    ob_clean();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get and validate input
    $order_id = intval($_POST['order_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 0);
    
    if ($order_id <= 0) {
        throw new Exception('Valid order ID is required');
    }
    
    if ($quantity <= 0) {
        throw new Exception('Quantity must be greater than 0');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Debug: Log the values being used
    error_log("Update quantity debug - Order ID: $order_id, Cashier ID: $cashier_id, Store ID: $store_id, New Quantity: $quantity");

    // Verify the order belongs to the current cashier and store, and is in Cart status
    $stmt = $conn->prepare("SELECT id, product_name, quantity, price, cashier_id, store_id, status FROM orders
                           WHERE id = ? AND cashier_id = ? AND store_id = ? AND status = 'Cart'");
    $stmt->bind_param('iii', $order_id, $cashier_id, $store_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Debug: Check if the order exists at all
        $debug_stmt = $conn->prepare("SELECT id, cashier_id, store_id, status FROM orders WHERE id = ?");
        $debug_stmt->bind_param('i', $order_id);
        $debug_stmt->execute();
        $debug_result = $debug_stmt->get_result();

        if ($debug_result->num_rows === 0) {
            throw new Exception("Order ID $order_id does not exist");
        } else {
            $debug_order = $debug_result->fetch_assoc();
            throw new Exception("Order access denied. Order cashier_id: {$debug_order['cashier_id']}, Current cashier_id: $cashier_id, Order store_id: {$debug_order['store_id']}, Current store_id: $store_id, Order status: {$debug_order['status']}");
        }
    }

    $order = $result->fetch_assoc();

    // Debug: Log the order details found
    error_log("Found order: " . json_encode($order));

    // Update the quantity - Use a simpler approach with just the order ID since we already verified ownership
    $stmt = $conn->prepare("UPDATE orders SET quantity = ?, order_date = NOW() WHERE id = ?");
    $stmt->bind_param('ii', $quantity, $order_id);

    if (!$stmt->execute()) {
        throw new Exception('Failed to update cart item quantity: ' . $stmt->error);
    }

    if ($stmt->affected_rows === 0) {
        throw new Exception("No rows affected. Order ID: $order_id may have been modified by another process.");
    }

    // Calculate new payable amount (quantity * price)
    $new_payable = $quantity * $order['price'];

    echo json_encode([
        'status' => 'success',
        'message' => 'Quantity updated successfully',
        'data' => [
            'order_id' => $order_id,
            'product_name' => $order['product_name'],
            'old_quantity' => $order['quantity'],
            'new_quantity' => $quantity,
            'price' => $order['price'],
            'new_payable' => $new_payable
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
