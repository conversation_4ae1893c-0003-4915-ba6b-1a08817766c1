<?php
require_once __DIR__ . '/../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Check if user is authenticated
    if (!isset($_SESSION['auth_user']['uid'])) {
        throw new Exception('User not authenticated');
    }

    // Get and validate input
    $order_id = intval($_POST['order_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 0);
    
    if ($order_id <= 0) {
        throw new Exception('Valid order ID is required');
    }
    
    if ($quantity <= 0) {
        throw new Exception('Quantity must be greater than 0');
    }

    // Get user information
    $cashier_id = $_SESSION['auth_user']['uid'];

    // Get store_id from session or GET parameter
    $store_id = null;
    if (isset($_GET['storeID']) || isset($_SESSION['auth_user']['sys_store_assigned'])) {
        if (empty($_GET['storeID']) || $_GET['storeID'] == 0) {
            $store_id = $_SESSION['auth_user']['sys_store_assigned'];
        } else {
            $store_id = $_GET['storeID'];
        }
    }

    if (empty($store_id)) {
        throw new Exception('Store ID not found');
    }

    // Verify the order belongs to the current cashier and store, and is in Cart status
    $stmt = $conn->prepare("SELECT id, product_name, quantity, price FROM orders 
                           WHERE id = ? AND cashier_id = ? AND store_id = ? AND status = 'Cart'");
    $stmt->bind_param('iii', $order_id, $cashier_id, $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Order item not found or access denied');
    }

    $order = $result->fetch_assoc();

    // Update the quantity
    $stmt = $conn->prepare("UPDATE orders SET quantity = ?, order_date = NOW() 
                           WHERE id = ? AND cashier_id = ? AND store_id = ? AND status = 'Cart'");
    $stmt->bind_param('iiii', $quantity, $order_id, $cashier_id, $store_id);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to update cart item quantity: ' . $stmt->error);
    }

    if ($stmt->affected_rows === 0) {
        throw new Exception('No item was updated. Item may not exist or access denied.');
    }

    // Calculate new payable amount (quantity * price)
    $new_payable = $quantity * $order['price'];

    echo json_encode([
        'status' => 'success',
        'message' => 'Quantity updated successfully',
        'data' => [
            'order_id' => $order_id,
            'product_name' => $order['product_name'],
            'old_quantity' => $order['quantity'],
            'new_quantity' => $quantity,
            'price' => $order['price'],
            'new_payable' => $new_payable
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}
