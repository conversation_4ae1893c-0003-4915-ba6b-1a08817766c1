<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX Debug Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>AJAX Debug Test</h1>
    
    <div>
        <h2>Test Simple JSON Endpoint</h2>
        <button id="testSimple">Test Simple JSON</button>
        <div id="simpleResult"></div>
    </div>
    
    <div>
        <h2>Test DB Connection Endpoint</h2>
        <button id="testDB">Test DB Connection</button>
        <div id="dbResult"></div>
    </div>
    
    <div>
        <h2>Test Add to Cart</h2>
        <input type="text" id="productCode" placeholder="Product Code" value="4800361394161">
        <button id="testAddCart">Test Add to Cart</button>
        <div id="addCartResult"></div>
    </div>
    
    <div>
        <h2>Test Get Cart Items</h2>
        <button id="testGetCart">Test Get Cart</button>
        <div id="getCartResult"></div>
    </div>
    
    <script>
        function logResponse(title, xhr, response) {
            console.log('=== ' + title + ' ===');
            console.log('Status:', xhr.status);
            console.log('Status Text:', xhr.statusText);
            console.log('Response Headers:', xhr.getAllResponseHeaders());
            console.log('Response Text:', xhr.responseText);
            console.log('Parsed Response:', response);
            console.log('========================');
        }
        
        $('#testSimple').click(function() {
            $.ajax({
                url: 'test_json.php',
                type: 'GET',
                success: function(response, textStatus, xhr) {
                    logResponse('Simple JSON Test - Success', xhr, response);
                    $('#simpleResult').html('<pre style="color: green;">' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    logResponse('Simple JSON Test - Error', xhr, null);
                    $('#simpleResult').html('<pre style="color: red;">Error: ' + error + '\nStatus: ' + xhr.status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        });
        
        $('#testDB').click(function() {
            $.ajax({
                url: 'test_with_db.php',
                type: 'GET',
                success: function(response, textStatus, xhr) {
                    logResponse('DB Connection Test - Success', xhr, response);
                    $('#dbResult').html('<pre style="color: green;">' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    logResponse('DB Connection Test - Error', xhr, null);
                    $('#dbResult').html('<pre style="color: red;">Error: ' + error + '\nStatus: ' + xhr.status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        });
        
        $('#testAddCart').click(function() {
            const productCode = $('#productCode').val();
            $.ajax({
                url: 'add_to_cart.php',
                type: 'POST',
                data: {
                    product_code: productCode
                },
                success: function(response, textStatus, xhr) {
                    logResponse('Add to Cart Test - Success', xhr, response);
                    $('#addCartResult').html('<pre style="color: green;">' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    logResponse('Add to Cart Test - Error', xhr, null);
                    $('#addCartResult').html('<pre style="color: red;">Error: ' + error + '\nStatus: ' + xhr.status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        });
        
        $('#testGetCart').click(function() {
            $.ajax({
                url: 'get_cart_items.php',
                type: 'GET',
                success: function(response, textStatus, xhr) {
                    logResponse('Get Cart Test - Success', xhr, response);
                    $('#getCartResult').html('<pre style="color: green;">' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    logResponse('Get Cart Test - Error', xhr, null);
                    $('#getCartResult').html('<pre style="color: red;">Error: ' + error + '\nStatus: ' + xhr.status + '\nResponse: ' + xhr.responseText + '</pre>');
                }
            });
        });
    </script>
</body>
</html>
