<?php
require_once __DIR__ . '/../db_conn.php';
require_once __DIR__ . '/../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

use PhpOffice\PhpSpreadsheet\Style\Fill;

// Check if user is authenticated
if (!isset($_SESSION['auth']) || $_SESSION['auth'] !== true) {
    http_response_code(401);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

try {
    // Get store ID from URL parameter
    $store_id = isset($_GET['storeID']) ? intval($_GET['storeID']) : 0;
    if ($store_id <= 0) {
        throw new Exception('Invalid or missing store ID');
    }

    // Get optional category filter from URL parameter
    $category_id = isset($_GET['categoryID']) ? intval($_GET['categoryID']) : 0;

    // Get store name for filename
    $store_sql = "SELECT client FROM store_info WHERE id = ?";
    $store_stmt = $conn->prepare($store_sql);
    if (!$store_stmt) {
        throw new Exception('Failed to prepare store query: ' . $conn->error);
    }

    $store_stmt->bind_param('i', $store_id);
    $store_stmt->execute();
    $store_result = $store_stmt->get_result();

    if ($store_result->num_rows === 0) {
        throw new Exception('Store not found with ID: ' . $store_id);
    }

    $store_data = $store_result->fetch_assoc();
    $store_name = $store_data['client'];
    $store_stmt->close();

    // Create new spreadsheet
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Pricing Template');

    // Define headers
    $headers = [
        'A1' => 'CATEGORY',
        'B1' => 'BARCODE',
        'C1' => 'PRODUCT NAME',
        'D1' => 'PRODUCT DESCRIPTION',
        'E1' => 'SELLING PRICE',
        'F1' => 'COST PRICE'
    ];

    // Set headers
    foreach ($headers as $cell => $header) {
        $sheet->setCellValue($cell, $header);
    }

    // Style the header row
    $headerRange = 'A1:F1';
    $sheet->getStyle($headerRange)->getFont()->setBold(true);
    $sheet->getStyle($headerRange)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setRGB('4472C4');
    $sheet->getStyle($headerRange)->getFont()->getColor()->setRGB('FFFFFF');
    $sheet->getStyle($headerRange)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

    // Fetch existing products for this store (with optional category filter)
    $sql = "SELECT
                sp.store_id,
                c.category as category_name,
                sp.product_code,
                sp.product_name,
                sp.product_description,
                sp.selling_price,
                sp.cost_price
            FROM store_products sp
            LEFT JOIN category c ON sp.category_id = c.id
            WHERE sp.store_id = ?";

    // Add category filter if specified
    $params = [$store_id];
    $param_types = 'i';

    if ($category_id > 0) {
        $sql .= " AND sp.category_id = ?";
        $params[] = $category_id;
        $param_types .= 'i';
    }

    $sql .= " ORDER BY sp.product_name";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $row_number = 2; // Start from row 2 (after headers)
    $has_data = false;

    while ($product = $result->fetch_assoc()) {
        $has_data = true;
        
        // Populate data
        $sheet->setCellValue('A' . $row_number, $product['category_name']);
        $sheet->setCellValue('B' . $row_number, $product['product_code']);
        $sheet->setCellValue('C' . $row_number, $product['product_name']);
        $sheet->setCellValue('D' . $row_number, $product['product_description']);
        $sheet->setCellValue('E' . $row_number, 0);
        $sheet->setCellValue('F' . $row_number, 0);
        
        $row_number++;
    }

    // If no data exists, add a sample row with instructions
    if (!$has_data) {
        $sheet->setCellValue('A2', 'Sample Category');
        $sheet->setCellValue('B2', 'SAMPLE001');
        $sheet->setCellValue('C2', 'Sample Product');
        $sheet->setCellValue('D2', 'Sample description');
        $sheet->setCellValue('E2', '100');
        $sheet->setCellValue('F2', '100');

        // Add instruction row
        $sheet->setCellValue('A3', 'Instructions:');
        $sheet->setCellValue('B3', 'Replace this sample data with your actual product information');
        $sheet->getStyle('A3:J3')->getFont()->setItalic(true);
        $sheet->getStyle('A3:J3')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFF2CC');
    }

    // Auto-size columns
    foreach (range('A', 'F') as $column) {
        $sheet->getColumnDimension($column)->setAutoSize(true);
    }

    // Add instructions sheet
    $instructionsSheet = $spreadsheet->createSheet();
    $instructionsSheet->setTitle('Instructions');
    
    $instructions = [
        ['PRICING IMPORT INSTRUCTIONS'],
        [''],
        ['1. REQUIRED COLUMNS:'],
        ['   - product_code: Unique product barcode/SKU (PRIMARY IDENTIFIER)'],
        ['   - product_qty: New quantity to set (must be a number >= 0)'],
        [''],
        ['2. OPTIONAL COLUMNS (for fallback matching):'],
        ['   - product_name: Product name (tertiary matching)'],
        [''],
        ['3. MATCHING PRIORITY (with multi-tenant security):'],
        ['   - Products are matched in this order:'],
        ['   - 1st: By product_code + store_id (PRIMARY - REQUIRED)'],
        ['   - 2nd: By product_name + store_id (final fallback)'],
        [''],
        ['4. IMPORTANT NOTES:'],
        ['   - product_code is REQUIRED and must match exactly'],
        ['   - Only product_qty will be updated'],
        ['   - Products must exist in your store'],
        ['   - Quantity must be a non-negative number'],
        ['   - Empty rows will be skipped'],
        ['   - Maximum file size: 10MB'],
        ['   - All matching uses store_id for security isolation'],
        [''],
        ['5. SUPPORTED FILE FORMATS:'],
        ['   - Excel (.xlsx, .xls)'],
        ['   - CSV (.csv)'],
        [''],
        ['6. TIPS:'],
        ['   - Use the "Pricing Template" sheet with your existing products'],
        ['   - Update only the product_qty column'],
        ['   - Keep other columns for reference'],
        ['   - Save as Excel format for best compatibility']
    ];

    $row = 1;
    foreach ($instructions as $instruction) {
        $instructionsSheet->setCellValue('A' . $row, $instruction[0]);
        if ($row === 1) {
            // Title formatting
            $instructionsSheet->getStyle('A' . $row)->getFont()->setBold(true)->setSize(14);
        } elseif (strpos($instruction[0], '. ') !== false && strlen($instruction[0]) < 30) {
            // Section headers
            $instructionsSheet->getStyle('A' . $row)->getFont()->setBold(true);
        }
        $row++;
    }
    
    $instructionsSheet->getColumnDimension('A')->setWidth(80);

    // Set the active sheet back to the template
    $spreadsheet->setActiveSheetIndex(0);

    // Generate filename with store name and timestamp
    $timestamp = date('Y-m-d_H-i-s');

    // Sanitize store name for filename (remove invalid characters)
    $safe_store_name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $store_name);
    $safe_store_name = trim($safe_store_name);

    // Get category name for filename if filtering by category
    $category_suffix = '';
    if ($category_id > 0) {
        $category_sql = "SELECT category FROM category WHERE id = ? AND store_id = ?";
        $category_stmt = $conn->prepare($category_sql);
        $category_stmt->bind_param('ii', $category_id, $store_id);
        $category_stmt->execute();
        $category_result = $category_stmt->get_result();

        if ($category_result->num_rows > 0) {
            $category_data = $category_result->fetch_assoc();
            $safe_category_name = preg_replace('/[^a-zA-Z0-9\-_\s]/', '', $category_data['category']);
            $safe_category_name = trim($safe_category_name);
            $category_suffix = "-{$safe_category_name}";
        }
        $category_stmt->close();
    }

    // Generate filename: Pricing-{store_name}[-{category}]-{datetime}.xlsx
    $filename = "Pricing-{$safe_store_name}{$category_suffix}-{$timestamp}.xlsx";

    // Clean any output buffer to prevent corruption
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');

    // Create writer and output
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');

} catch (Exception $e) {
    // If there's an error, return JSON response with detailed logging
    error_log("Template generation exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Only set headers if they haven't been sent yet
    if (!headers_sent()) {
        header('Content-Type: application/json');
        http_response_code(400);
    }
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to generate template: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
} catch (Error $e) {
    // Handle fatal errors with detailed logging
    error_log("Template generation fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Only set headers if they haven't been sent yet
    if (!headers_sent()) {
        header('Content-Type: application/json');
        http_response_code(500);
    }
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error while generating template: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
