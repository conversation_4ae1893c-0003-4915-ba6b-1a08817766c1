<?php
require_once __DIR__ . '/../../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get DataTables parameters
    $draw = intval($_POST['draw'] ?? 1);
    $start = intval($_POST['start'] ?? 0);
    $length = intval($_POST['length'] ?? 10);
    $search_value = $_POST['search']['value'] ?? '';
    $order_column = intval($_POST['order'][0]['column'] ?? 0);
    $order_dir = $_POST['order'][0]['dir'] ?? 'asc';

    // Get and sanitize storeID parameter
    $storeID = isset($_POST['storeID']) ? intval(trim($_POST['storeID'])) : 0;

    // Validate storeID - must be a positive integer
    if ($storeID <= 0) {
        throw new Exception('Invalid or missing store ID');
    }

    // Column mapping for ordering (with table aliases)
    $columns = [
        0 => 'sp.product_code',
        1 => 'c.category',
        2 => 'sp.product_image',
        3 => 'sp.product_name',
        4 => 'sp.product_description',
        5 => 'sp.product_qty',
        6 => 'sp.selling_price',
        7 => 'sp.cost_price'
    ];

    $order_column_name = $columns[$order_column] ?? 'sp.product_name';

    // Base query with LEFT JOIN to category table - using prepared statement
    $base_query = "FROM store_products sp LEFT JOIN category c ON sp.category_id = c.id WHERE sp.store_id = ?";
    $params = [$storeID];
    $param_types = "i";

    // Add store filtering if needed (placeholder for now)
    // TODO: Add proper store filtering based on user session
    // $base_query .= " AND store_id = ?";
    // $params[] = $store_id;
    // $param_types .= "i";

    // Add search filtering
    if (!empty($search_value)) {
        $base_query .= " AND (sp.product_code LIKE ? OR sp.product_name LIKE ? OR sp.product_description LIKE ? OR CAST(sp.product_qty AS CHAR) LIKE ? OR c.category LIKE ? OR CAST(sp.selling_price AS CHAR) LIKE ? OR CAST(sp.cost_price AS CHAR) LIKE ?)";
        $search_param = "%{$search_value}%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param, $search_param, $search_param]);
        $param_types .= "sssssss";
    }

    // Get total records count
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $stmt = $conn->prepare($total_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $total_result = $stmt->get_result();
    $total_records = $total_result->fetch_assoc()['total'];

    // Get filtered records count (same as total if no search)
    $filtered_records = $total_records;

    // Get actual data with ordering and pagination
    $data_query = "SELECT sp.id, sp.product_code, sp.category_id, c.category as category_name, sp.product_name, sp.product_description, sp.product_qty, sp.product_image, sp.selling_price, sp.cost_price " .
                  $base_query .
                  " ORDER BY {$order_column_name} {$order_dir} LIMIT ?, ?";

    $params[] = $start;
    $params[] = $length;
    $param_types .= "ii";

    $stmt = $conn->prepare($data_query);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            htmlspecialchars($row['product_code']), // Product code
            htmlspecialchars($row['category_name'] ?? 'Uncategorized'), // Category name or fallback
            $row['product_image'] ? '<img src="../img_products/' . $row['product_image'] . '" width="30" height="30" />' : '',
            htmlspecialchars($row['product_name']),
            htmlspecialchars($row['product_description'] ?? ''),
            intval($row['product_qty']),
            number_format($row['selling_price'], 2),
            number_format($row['cost_price'], 2)
        ];
    }

    // Return DataTables format
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Error response
    $error_response = [
        'draw' => intval($_POST['draw'] ?? 1),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Server error occurred'
    ];
    
    echo json_encode($error_response);
}
?>
