<?php
require_once __DIR__ . '/../../db_conn.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get store ID from request
    $storeID = null;
    if (isset($_POST['storeID'])) {
        $storeID = intval($_POST['storeID']);
    } elseif (isset($_GET['storeID'])) {
        $storeID = intval($_GET['storeID']);
    }

    if (!$storeID || $storeID <= 0) {
        throw new Exception('Valid store ID is required');
    }

    // Fetch categories for the specified store
    $sql = "SELECT id, category, description FROM category WHERE store_id = ? ORDER BY category ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $storeID);
    $stmt->execute();
    $result = $stmt->get_result();

    $categories = [];
    while ($row = $result->fetch_assoc()) {
        $categories[] = [
            'id' => intval($row['id']),
            'category' => htmlspecialchars($row['category']),
            'description' => htmlspecialchars($row['description'] ?? '')
        ];
    }

    // Return categories as JSON
    echo json_encode($categories);

} catch (Exception $e) {
    // Error response
    http_response_code(400);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    // Fatal error response
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'A system error occurred: ' . $e->getMessage()
    ]);
}
?>
