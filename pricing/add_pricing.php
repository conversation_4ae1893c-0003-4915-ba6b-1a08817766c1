<?php
require_once __DIR__ . '/../db_conn.php';
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../utils/import_logger.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

// Set content type to JSO<PERSON> for AJAX responses
header('Content-Type: application/json');

// Check if user is authenticated
if (!isset($_SESSION['auth']) || $_SESSION['auth'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Authentication required']);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

try {
    // Validate required parameters
    if (!isset($_POST['add_pricing']) || !isset($_FILES['pricing_file'])) {
        throw new Exception('Missing required parameters');
    }

    // Get store ID from URL parameter
    $store_id = isset($_GET['storeID']) ? intval($_GET['storeID']) : 0;
    if ($store_id <= 0) {
        throw new Exception('Invalid or missing store ID');
    }

    // Validate file upload
    $file = $_FILES['pricing_file'];
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed: ' . $file['error']);
    }

    // Validate file type
    $allowed_extensions = ['xlsx', 'xls', 'csv'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed.');
    }

    // Validate file size (max 10MB)
    $max_size = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $max_size) {
        throw new Exception('File size too large. Maximum allowed size is 10MB.');
    }

    // Load the spreadsheet
    $spreadsheet = IOFactory::load($file['tmp_name']);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();

    if (empty($rows)) {
        throw new Exception('The uploaded file is empty or could not be read.');
    }

    // Define expected headers (must match download template exactly)
    $expected_headers = [
        'A1' => 'CATEGORY',
        'B1' => 'BARCODE',
        'C1' => 'PRODUCT NAME',
        'D1' => 'PRODUCT DESCRIPTION',
        'E1' => 'SELLING PRICE',
        'F1' => 'COST PRICE'
    ];

    // Get actual headers from uploaded file (first row)
    $actual_headers = array_map('trim', array_map('strtoupper', $rows[0]));

    // Validate headers match exactly
    $header_errors = [];
    $expected_header_values = array_values($expected_headers);

    // Check if we have the right number of columns
    if (count($actual_headers) < count($expected_header_values)) {
        throw new Exception('Invalid file format. Expected ' . count($expected_header_values) . ' columns, found ' . count($actual_headers) . '. Please use the template downloaded from the system.');
    }

    // Validate each header position
    for ($i = 0; $i < count($expected_header_values); $i++) {
        $expected = $expected_header_values[$i];
        $actual = isset($actual_headers[$i]) ? $actual_headers[$i] : '';

        if ($actual !== $expected) {
            $column_letter = chr(65 + $i); // A, B, C, D, E, F
            $header_errors[] = "Column {$column_letter}: Expected '{$expected}', found '{$actual}'";
        }
    }

    // If headers don't match, reject the file
    if (!empty($header_errors)) {
        $error_message = "Invalid file headers. Please use the template downloaded from the system.\n\nHeader errors:\n" . implode("\n", $header_errors);
        throw new Exception($error_message);
    }

    // Define column mapping (0-based indices)
    $column_map = [
        'category' => 0,        // Column A
        'barcode' => 1,         // Column B
        'product_name' => 2,    // Column C
        'description' => 3,     // Column D
        'selling_price' => 4,   // Column E
        'cost_price' => 5       // Column F
    ];

    // Initialize counters
    $total_rows = count($rows) - 1; // Exclude header
    $processed = 0;
    $updated = 0;
    $errors = [];
    $warnings = [];
    $info_messages = [];
    $skipped = 0;

    // Initialize logging
    $logger = new ImportLogger($conn);
    $current_user = getCurrentUser();
    $import_log_id = $logger->logImportStart(
        $store_id,
        'pricing_import',
        'Pricing',
        $file['name'],
        $current_user
    );

    if (!$import_log_id) {
        error_log("Warning: Failed to create import log entry for file: " . $file['name']);
    }

    // Process each data row
    for ($i = 1; $i < count($rows); $i++) {
        $row = $rows[$i];
        $row_number = $i + 1;

        try {
            $processed++;

            // Extract data from row using new column mapping
            $category = isset($row[$column_map['category']]) ? trim($row[$column_map['category']]) : '';
            $barcode = isset($row[$column_map['barcode']]) ? trim($row[$column_map['barcode']]) : '';
            $product_name = isset($row[$column_map['product_name']]) ? trim($row[$column_map['product_name']]) : '';
            $description = isset($row[$column_map['description']]) ? trim($row[$column_map['description']]) : '';
            $selling_price = isset($row[$column_map['selling_price']]) ? $row[$column_map['selling_price']] : '';
            $cost_price = isset($row[$column_map['cost_price']]) ? $row[$column_map['cost_price']] : '';

            // Skip completely empty rows
            if (empty($barcode) && empty($product_name) && empty($selling_price) && empty($cost_price)) {
                $skipped++;
                // Log skipped row
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: 'empty',
                    $product_name ?: 'empty',
                    ($selling_price ?: 'empty') . '|' . ($cost_price ?: 'empty'),
                    'skipped_empty_row',
                    $current_user
                );
                continue;
            }

            // Validate that either barcode or product name is provided for product matching
            if (empty($barcode) && empty($product_name)) {
                $errors[] = "Row {$row_number}: Either Barcode (Column B) or Product Name (Column C) is required for product matching.";
                // Log validation error
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    'missing',
                    'missing',
                    ($selling_price ?: 'unknown') . '|' . ($cost_price ?: 'unknown'),
                    'failed_missing_identifier',
                    $current_user
                );
                continue;
            }

            // Validate selling price
            if (!is_numeric($selling_price) || $selling_price < 0) {
                $errors[] = "Row {$row_number}: Invalid selling price value '{$selling_price}' (Column E). Must be a non-negative number.";
                // Log validation error
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: 'N/A',
                    $product_name ?: 'unknown',
                    $selling_price . '|' . ($cost_price ?: 'unknown'),
                    'failed_invalid_selling_price',
                    $current_user
                );
                continue;
            }

            // Validate cost price
            if (!is_numeric($cost_price) || $cost_price < 0) {
                $errors[] = "Row {$row_number}: Invalid cost price value '{$cost_price}' (Column F). Must be a non-negative number.";
                // Log validation error
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: 'N/A',
                    $product_name ?: 'unknown',
                    ($selling_price ?: 'unknown') . '|' . $cost_price,
                    'failed_invalid_cost_price',
                    $current_user
                );
                continue;
            }

            // Find the product in database with fallback matching (multi-tenant safe)
            $product = null;
            $match_method = '';

            // Primary match: Try barcode first if provided
            if (!empty($barcode)) {
                $stmt = $conn->prepare("SELECT id, product_name, product_code, selling_price, cost_price FROM store_products WHERE product_code = ? AND store_id = ? LIMIT 1");
                $stmt->bind_param('si', $barcode, $store_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $product = $result->fetch_assoc();
                $match_method = 'barcode';
            }

            // Fallback match: Try product name if barcode didn't match and product name is provided
            if (!$product && !empty($product_name)) {
                $stmt = $conn->prepare("SELECT id, product_name, product_code, selling_price, cost_price FROM store_products WHERE product_name = ? AND store_id = ? LIMIT 1");
                $stmt->bind_param('si', $product_name, $store_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $product = $result->fetch_assoc();
                $match_method = 'product_name';
            }

            if (!$product) {
                $identifier = !empty($barcode) ? "barcode '{$barcode}'" : "product name '{$product_name}'";
                $errors[] = "Row {$row_number}: Product with {$identifier} not found in store {$store_id}. Ensure the identifier matches exactly with existing products.";
                // Log product not found error
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: 'N/A',
                    $product_name ?: 'N/A',
                    $selling_price . '|' . $cost_price,
                    'failed_product_not_found',
                    $current_user
                );
                continue;
            }

            // Optional: Validate product name matches when matched by barcode (for additional verification)
            if ($match_method === 'barcode' && !empty($product_name) && strcasecmp(trim($product['product_name']), $product_name) !== 0) {
                // This is a warning, not an error - still proceed with update
                $warnings[] = "Row {$row_number}: Warning - Product name '{$product_name}' doesn't match database name '{$product['product_name']}' for barcode '{$barcode}'. Pricing updated anyway.";
            }

            // Add info message about which matching method was used
            if ($match_method === 'product_name') {
                $info_messages[] = "Row {$row_number}: Product matched by name '{$product_name}' (Barcode: {$product['product_code']}).";
            }

            // UPDATE BEHAVIOR: Replace existing prices with imported prices (not add)
            $current_selling_price = floatval($product['selling_price']);
            $current_cost_price = floatval($product['cost_price']);

            // Use imported prices directly (replace existing prices)
            $final_selling_price = floatval($selling_price);
            $final_cost_price = floatval($cost_price);

            // Update the product prices (replace existing prices with imported values)
            $update_stmt = $conn->prepare("UPDATE store_products SET selling_price = ?, cost_price = ?, updated_at = NOW() WHERE id = ? AND store_id = ?");
            $update_stmt->bind_param('ddii', $final_selling_price, $final_cost_price, $product['id'], $store_id);

            if ($update_stmt->execute()) {
                $updated++;
                // Add informational message about the pricing change
                $info_messages[] = "Row {$row_number}: Product '{$product['product_name']}' (Barcode: {$barcode}) selling price updated from ₱{$current_selling_price} to ₱{$final_selling_price}.";
                $info_messages[] = "Row {$row_number}: Product '{$product['product_name']}' (Barcode: {$barcode}) cost price updated from ₱{$current_cost_price} to ₱{$final_cost_price}.";

                // Log successful update
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: $product['product_code'],
                    $product['product_name'],
                    'Selling: ₱' . $final_selling_price . ' | Cost: ₱' . $final_cost_price.' ',
                    ' ✅',
                    $current_user
                );
            } else {
                $errors[] = "Row {$row_number}: Failed to update pricing for product '{$product['product_name']}' (Barcode: {$product['product_code']}) - " . $update_stmt->error;

                // Log update failure
                $logger->logAuditRow(
                    $store_id,
                    'pricing_row_processing',
                    'Update Pricing',
                    $barcode ?: $product['product_code'],
                    $product['product_name'],
                    $selling_price . '|' . $cost_price,
                    'failed_update_error',
                    $current_user
                );
            }

        } catch (Exception $e) {
            $errors[] = "Row {$row_number}: " . $e->getMessage();

            // Log exception
            $logger->logAuditRow(
                $store_id,
                'pricing_row_processing',
                'Update Pricing',
                $barcode ?: 'unknown',
                $product_name ?: 'unknown',
                ($selling_price ?: 'unknown') . '|' . ($cost_price ?: 'unknown'),
                'failed_exception',
                $current_user
            );
        }
    }

    // Prepare response with separate message types
    $response = [
        'status' => 'success',
        'message' => "Import completed successfully!",
        'data' => [
            'total_rows' => $total_rows,
            'processed' => $processed,
            'updated' => $updated,
            'skipped' => $skipped,
            'errors' => $errors,
            'warnings' => $warnings,
            'info_messages' => $info_messages,
            'error_count' => count($errors),
            'warning_count' => count($warnings),
            'info_count' => count($info_messages)
        ]
    ];

    // Determine status based on actual errors only (not warnings or info messages)
    if (!empty($errors) && $updated > 0) {
        $response['status'] = 'partial_success';
        $response['message'] = "Import completed with some errors. {$updated} products updated successfully.";
    }
    // If no updates succeeded and there are errors
    elseif ($updated === 0 && !empty($errors)) {
        $response['status'] = 'error';
        $response['message'] = "Import failed. No products were updated.";
    }
    // If no updates succeeded but no errors (likely all rows were skipped)
    elseif ($updated === 0) {
        $response['status'] = 'warning';
        $response['message'] = "No products were updated. Please check your file format and data.";
    }
    // Success with possible warnings but no errors
    elseif (!empty($warnings) && empty($errors)) {
        $response['status'] = 'success_with_warnings';
        $response['message'] = "Import completed successfully with {$updated} products updated. Some warnings were generated.";
    }

    // Update the import log with final status
    if ($import_log_id) {
        $final_status = $response['status'];
        $stats = [
            'total_rows' => $total_rows,
            'processed' => $processed,
            'updated' => $updated,
            'skipped' => $skipped,
            'error_count' => count($errors),
            'warning_count' => count($warnings)
        ];

        $logger->logImportEnd($import_log_id, $final_status, $stats);
    }

    echo json_encode($response);

} catch (Exception $e) {
    // Update import log with error status if it was created
    if (isset($import_log_id) && $import_log_id) {
        $logger->logImportEnd($import_log_id, 'error', ['error_message' => $e->getMessage()]);
    }

    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'data' => [
            'total_rows' => 0,
            'processed' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => [$e->getMessage()],
            'error_count' => 1
        ]
    ]);
} catch (Error $e) {
    // Update import log with fatal error status if it was created
    if (isset($import_log_id) && $import_log_id) {
        $logger->logImportEnd($import_log_id, 'fatal_error', ['error_message' => $e->getMessage()]);
    }

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage(),
        'data' => [
            'total_rows' => 0,
            'processed' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => ['Internal server error occurred'],
            'error_count' => 1
        ]
    ]);
}
?>
