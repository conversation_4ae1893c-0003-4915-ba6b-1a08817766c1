<!-- add pricing modal -->
<div class="modal fade" id="modal-pricing" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content">
					<div class="modal-header">
							<h3 class="modal-title font-weight-bold text-dark"><i class="fal fa-circle"></i> P R I C I N G</h3>
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
									<span aria-hidden="true"><i class="fal fa-times"></i></span>
							</button>
					</div>

					<form id="form-pricing" method="POST" enctype="multipart/form-data" class="p-4">
						<div class="modal-body rounded" style="background-color: #E5F1F5FF;">

							<!-- File Upload Section -->
							<div class="form-group">
								<label for="pricing_file" class="text-dark font-weight-bold">Pricing File <code>*</code></label>
								<input type="file" class="form-control" id="pricing_file" name="pricing_file" accept=".csv,.xlsx,.xls" required>
								<small class="form-text text-muted">
									Supported formats: Excel (.xlsx, .xls) and CSV (.csv). Maximum file size: 10MB.
								</small>
							</div>

							<!-- Instructions -->
							<div class="alert alert-info" role="alert">
								<h6 class="alert-heading"><i class="fal fa-info-circle"></i> Import Instructions:</h6>
								<ul class="mb-0 small">
									<li>Download the template to get your current pricing data</li>
									<li>Update only the <strong>selling_price</strong> and <strong>cost_price</strong> columns with new quantities</li>
									<li>Products are matched by ID, product code</li>
									<li>Only existing products will be updated</li>
								</ul>
							</div>

							<!-- Progress Bar (hidden by default) -->
							<div id="import-progress" class="form-group" style="display: none;">
								<label class="text-dark font-weight-bold">Import Progress:</label>
								<div class="progress">
									<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
								</div>
								<small class="text-muted">Processing your file...</small>
							</div>

							<!-- Results Section (hidden by default) -->
							<div id="import-results" class="form-group" style="display: none;">
								<label class="text-dark font-weight-bold">Import Results:</label>
								<div id="results-content" class="border rounded p-3 bg-white">
									<!-- Results will be populated here -->
								</div>
							</div>

							<input type="hidden" name="add_pricing" value="1">
							<input type="hidden" name="store_owner" value="<?php echo $_SESSION['auth_user']['uid']; ?>">
						</div>

						<div class="d-flex align-items-center border rounded p-2 mt-2">
								<!-- Category Filter for Template Download -->
								<div class="form-group mb-0 mr-3">
									<label for="template_category_filter" class="text-dark font-weight-bold mb-1" style="font-size: 12px;">Filter by Category:</label>
									<select id="template_category_filter" class="form-control form-control-sm" style="width: 200px;">
										<option value="">All Categories</option>
										<!-- Categories will be populated via AJAX -->
									</select>
								</div>
								<!-- Download Template Button -->
								<div class="form-group mb-0 mr-3">
									<div class=""><i class="fal fa-hand-point-down"></i></div>
									<button type="button" id="btn-download-template" class="btn btn-sm btn-outline-success">
										<i class="fal fa-file-excel"></i> Download Template
									</button>
								</div>
							</div>

						<div class="modal-footer d-flex">
							<div class="items-right ml-auto">
								<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
								<button type="submit" id="btnAddPricing" class="btn btn-primary">
									<span class="btn-text">Import Pricing</span>
									<span class="btn-loading" style="display: none;">
										<i class="fal fa-spinner fa-spin"></i> Importing...
									</span>
								</button>
							</div>
						</div>
					</form>
			</div>
	</div>
</div>

<!-- pricing Logs Modal -->
 <div class="modal fade modal-fullscreen example-modal-fullscreen" id="modal-pricing-logs" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog modal-fullscreen modal-dialog-centered" role="document">
				<div class="modal-content">
						<div class="modal-header">
								<h3 class="modal-title font-weight-bold text-dark"><i class="fal fa-history"></i> Pricing Logs	</h3>
								<button type="button" class="close" data-dismiss="modal" aria-label="Close">
										<span aria-hidden="true"><i class="fal fa-times"></i></span>
								</button>
							</div>
							<div class="modal-body rounded" style="background-color: #E5F1F5FF;">
								<div class="row">
									<div class="col-12">
										<div class="p-4">
												<!-- datatable start -->
											<table id="dt-pricing-logs" class="table table-bordered table-hover table-sm table-striped w-100 ">
													<thead class="bg-secondary text-light">
															<tr>
																	<th style="font-size:12px;">Barcode</th>
																	<th style="font-size:12px;">Category</th>
																	<th style="font-size:12px; width: 100px;">Image</th>
																	<th style="font-size:12px;">Product name</th>
																	<th style="font-size:12px;">Product description</th>
																	<th style="font-size:12px; background-color: orange;">Value audit</th>
																	<th style="font-size:12px;">Added By</th>
																	<th style="font-size:12px;">Date Added</th>
															</tr>
													</thead>
													<tbody></tbody>
											</table>
													<!-- datatable end -->
										</div>
									</div>
								</div>
							</div>
				</div>
		</div>
</div>

<!-- Import Logs Modal -->
 <div class="modal fade modal-fullscreen example-modal-fullscreen" id="modal-pricing-import-logs" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog modal-fullscreen modal-dialog-centered" role="document">
				<div class="modal-content">
						<div class="modal-header">
								<h3 class="modal-title font-weight-bold text-dark"><i class="fal fa-history"></i> <i class="fal fa-file-excel"></i> Import Logs	</h3>
								<button type="button" class="close" data-dismiss="modal" aria-label="Close">
										<span aria-hidden="true"><i class="fal fa-times"></i></span>
								</button>
							</div>
							<div class="modal-body rounded" style="background-color: #E5F1F5FF;">
								<div class="row">
									<div class="col-12">
										<div class="p-4">
												<!-- datatable start -->
											<table id="dt-pricing-import-logs" class="table table-bordered table-hover table-sm table-striped w-100 ">
													<thead class="bg-secondary text-light">
															<tr>
																	<th style="font-size:12px;">File</th>
																	<th style="font-size:12px;">Result</th>
																	<th style="font-size:12px;">User</th>
																	<th style="font-size:12px;">Date</th>
															</tr>
													</thead>
													<tbody></tbody>
											</table>
													<!-- datatable end -->
										</div>
									</div>
								</div>
							</div>
				</div>
		</div>
</div>